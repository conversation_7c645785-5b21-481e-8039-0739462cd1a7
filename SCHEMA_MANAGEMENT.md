# Schema Management Guide

This guide explains how to manage the Stack application database schema using the provided SQL scripts.

## 📁 Available Scripts

### 1. `schema.sql`
**Purpose**: Creates the complete Stack application database structure
**What it does**:
- Creates all tables, functions, triggers, and types
- Sets up Row Level Security (RLS) policies
- Enables realtime subscriptions
- Creates the user registration trigger

### 2. `fix-auth-policies.sql`
**Purpose**: Fixes the "Database error saving new user" issue
**What it does**:
- Adds missing INSERT policies for profiles and user_presence tables
- Fixes authentication flow without recreating everything

### 3. `clear_schema.sql`
**Purpose**: Completely removes ALL Stack application data and structure
**What it does**:
- Removes all tables, functions, triggers, and types
- Clears realtime publications
- **⚠️ WARNING**: Deletes ALL data permanently

### 4. `clear_schema_safe.sql`
**Purpose**: Removes Stack structure but preserves user authentication
**What it does**:
- Removes all Stack tables and functions
- Preserves `auth.users` table and authentication system
- Allows clean re-deployment without losing user accounts

## 🚀 Common Usage Scenarios

### Scenario 1: Fresh Installation
```sql
-- Run in Supabase SQL Editor
-- Copy and paste the contents of schema.sql
```

### Scenario 2: Fix Authentication Issues
```sql
-- If you get "Database error saving new user"
-- Run fix-auth-policies.sql
```

### Scenario 3: Update Schema (Recommended)
```sql
-- 1. First run the safe clear script
-- Copy and paste clear_schema_safe.sql

-- 2. Then run the updated schema
-- Copy and paste schema.sql
```

### Scenario 4: Complete Reset (Nuclear Option)
```sql
-- ⚠️ WARNING: This deletes EVERYTHING including users
-- Copy and paste clear_schema.sql

-- Then run schema.sql for fresh start
```

## 📋 Step-by-Step Instructions

### To Deploy Schema for First Time:

1. **Go to Supabase Dashboard**
   - Navigate to your project
   - Go to SQL Editor

2. **Run Schema**
   - Copy entire contents of `schema.sql`
   - Paste in SQL Editor
   - Click "Run"

3. **Enable Realtime**
   - Go to Database > Replication
   - Enable realtime for: `messages`, `user_presence`, `typing_indicators`, `reactions`, `server_members`

### To Fix Authentication Issues:

1. **Run Fix Script**
   - Copy contents of `fix-auth-policies.sql`
   - Paste in SQL Editor
   - Click "Run"

2. **Verify Fix**
   - Try user registration again
   - Should work without "Database error saving new user"

### To Update Schema:

1. **Safe Clear (Recommended)**
   - Copy contents of `clear_schema_safe.sql`
   - Paste in SQL Editor
   - Click "Run"
   - Verify completion message

2. **Re-deploy Schema**
   - Copy contents of updated `schema.sql`
   - Paste in SQL Editor
   - Click "Run"

3. **Re-enable Realtime**
   - Go to Database > Replication
   - Enable realtime for required tables

### To Completely Reset:

1. **⚠️ Nuclear Clear**
   - Copy contents of `clear_schema.sql`
   - Paste in SQL Editor
   - Click "Run"
   - **All data will be lost!**

2. **Fresh Deploy**
   - Copy contents of `schema.sql`
   - Paste in SQL Editor
   - Click "Run"

## 🔍 Verification

After running any script, check the completion messages and verification queries that run automatically.

### Check Tables Exist:
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%stack%' OR table_name IN (
    'profiles', 'servers', 'channels', 'messages'
);
```

### Check RLS Policies:
```sql
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE tablename IN ('profiles', 'user_presence')
ORDER BY tablename;
```

### Check Functions:
```sql
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%user%';
```

## ⚠️ Important Notes

1. **Always backup** before running clear scripts in production
2. **Test in development** environment first
3. **Clear scripts are irreversible** - data will be lost
4. **Safe clear preserves** `auth.users` but removes all app data
5. **Complete clear removes** everything including user accounts
6. **Re-enable realtime** after schema deployment
7. **Verify policies** are working with test user registration

## 🆘 Troubleshooting

### "Database error saving new user"
- Run `fix-auth-policies.sql`

### "Table already exists" errors
- Run appropriate clear script first

### "Permission denied" errors
- Check RLS policies are correctly set

### Realtime not working
- Verify tables are added to realtime publication
- Check RLS policies allow realtime access

---

**Remember**: Always test schema changes in a development environment before applying to production! 🚀
