-- Safe Schema Clear SQL - Removes Stack structures but preserves auth.users
-- This version keeps user authentication data intact
-- WARNING: This will still delete ALL application data (messages, servers, etc.)

-- ============================================================================
-- STEP 1: Remove Realtime Publications
-- ============================================================================

-- Remove tables from realtime publication
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS messages;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS user_presence;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS typing_indicators;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS reactions;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS server_members;

-- ============================================================================
-- STEP 2: Drop Triggers (but preserve auth trigger)
-- ============================================================================

-- Remove our custom trigger (but keep auth.users intact)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
DROP TRIGGER IF EXISTS update_servers_updated_at ON servers;
DROP TRIGGER IF EXISTS update_channels_updated_at ON channels;

-- ============================================================================
-- STEP 3: Disable RLS and Drop Policies First
-- ============================================================================

-- Disable RLS on all tables to avoid policy conflicts
ALTER TABLE IF EXISTS profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS servers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS server_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS channels DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS attachments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS reactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS user_presence DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS typing_indicators DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS channel_permissions DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS server_invites DISABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 4: Drop Tables (in reverse dependency order)
-- ============================================================================

-- Drop application tables (but preserve auth schema)
DROP TABLE IF EXISTS server_invites CASCADE;
DROP TABLE IF EXISTS channel_permissions CASCADE;
DROP TABLE IF EXISTS typing_indicators CASCADE;
DROP TABLE IF EXISTS user_presence CASCADE;
DROP TABLE IF EXISTS reactions CASCADE;
DROP TABLE IF EXISTS attachments CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS channels CASCADE;
DROP TABLE IF EXISTS server_members CASCADE;
DROP TABLE IF EXISTS servers CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;

-- ============================================================================
-- STEP 5: Drop Functions
-- ============================================================================

DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS cleanup_typing_indicators() CASCADE;
DROP FUNCTION IF EXISTS update_user_presence(UUID, user_status) CASCADE;

-- ============================================================================
-- STEP 6: Drop Custom Types
-- ============================================================================

DROP TYPE IF EXISTS user_status CASCADE;
DROP TYPE IF EXISTS channel_type CASCADE;
DROP TYPE IF EXISTS server_role CASCADE;
DROP TYPE IF EXISTS message_type CASCADE;

-- ============================================================================
-- STEP 7: Clean up any remaining policies (just in case)
-- ============================================================================

-- This will clean up any orphaned policies
DO $$
DECLARE
    pol_record RECORD;
BEGIN
    FOR pol_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE tablename IN (
            'profiles', 'servers', 'server_members', 'channels', 'messages', 
            'attachments', 'reactions', 'user_presence', 'typing_indicators', 
            'channel_permissions', 'server_invites'
        )
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
                      pol_record.policyname, 
                      pol_record.schemaname, 
                      pol_record.tablename);
    END LOOP;
END $$;

-- ============================================================================
-- VERIFICATION: Check what remains
-- ============================================================================

-- Check for any remaining Stack tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'profiles', 'servers', 'server_members', 'channels', 'messages', 
    'attachments', 'reactions', 'user_presence', 'typing_indicators', 
    'channel_permissions', 'server_invites'
);

-- Check for any remaining Stack functions
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'handle_new_user', 'update_updated_at_column', 
    'cleanup_typing_indicators', 'update_user_presence'
);

-- Check for any remaining Stack types
SELECT typname 
FROM pg_type 
WHERE typname IN ('user_status', 'channel_type', 'server_role', 'message_type');

-- Verify auth.users table is still intact
SELECT COUNT(*) as user_count FROM auth.users;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '============================================';
    RAISE NOTICE 'Safe schema cleanup completed!';
    RAISE NOTICE 'Stack application removed but auth.users';
    RAISE NOTICE 'and authentication system preserved.';
    RAISE NOTICE 'You can now re-run schema.sql safely.';
    RAISE NOTICE '============================================';
END $$;
