-- Stack Discord-like Application Database Schema
-- This file contains all tables, functions, triggers, and RLS policies

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> custom types
CREATE TYPE user_status AS ENUM ('online', 'away', 'busy', 'offline');
CREATE TYPE channel_type AS ENUM ('text', 'voice', 'category');
CREATE TYPE server_role AS ENUM ('owner', 'admin', 'moderator', 'member');
CREATE TYPE message_type AS ENUM ('text', 'system', 'join', 'leave');

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL CHECK (length(username) >= 2 AND length(username) <= 32),
    display_name TEXT CHECK (length(display_name) <= 32),
    avatar_url TEXT,
    status user_status DEFAULT 'offline',
    status_text TEXT CHECK (length(status_text) <= 128),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_seen TIMESTAMPTZ DEFAULT NOW()
);

-- Servers table
CREATE TABLE servers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 100),
    description TEXT CHECK (length(description) <= 500),
    icon_url TEXT,
    banner_url TEXT,
    owner_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    invite_code TEXT UNIQUE DEFAULT encode(gen_random_bytes(8), 'base64url'),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Server members table
CREATE TABLE server_members (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    server_id UUID REFERENCES servers(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    role server_role DEFAULT 'member',
    nickname TEXT CHECK (length(nickname) <= 32),
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(server_id, user_id)
);

-- Channels table
CREATE TABLE channels (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    server_id UUID REFERENCES servers(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL CHECK (length(name) >= 1 AND length(name) <= 100),
    description TEXT CHECK (length(description) <= 500),
    type channel_type DEFAULT 'text',
    parent_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    position INTEGER DEFAULT 0,
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Messages table
CREATE TABLE messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE NOT NULL,
    author_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    content TEXT CHECK (length(content) <= 2000),
    type message_type DEFAULT 'text',
    edited_at TIMESTAMPTZ,
    reply_to UUID REFERENCES messages(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Message attachments table
CREATE TABLE attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE NOT NULL,
    filename TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_size BIGINT,
    content_type TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Message reactions table
CREATE TABLE reactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    emoji TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(message_id, user_id, emoji)
);

-- User presence table for real-time status
CREATE TABLE user_presence (
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE PRIMARY KEY,
    status user_status DEFAULT 'offline',
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Typing indicators table
CREATE TABLE typing_indicators (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(channel_id, user_id)
);

-- Channel permissions table
CREATE TABLE channel_permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    role server_role,
    can_read BOOLEAN DEFAULT TRUE,
    can_write BOOLEAN DEFAULT TRUE,
    can_manage BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT check_user_or_role CHECK (
        (user_id IS NOT NULL AND role IS NULL) OR 
        (user_id IS NULL AND role IS NOT NULL)
    )
);

-- Server invites table
CREATE TABLE server_invites (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    server_id UUID REFERENCES servers(id) ON DELETE CASCADE NOT NULL,
    inviter_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    code TEXT UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(8), 'base64url'),
    max_uses INTEGER,
    uses INTEGER DEFAULT 0,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_status ON profiles(status);
CREATE INDEX idx_server_members_server_id ON server_members(server_id);
CREATE INDEX idx_server_members_user_id ON server_members(user_id);
CREATE INDEX idx_channels_server_id ON channels(server_id);
CREATE INDEX idx_channels_parent_id ON channels(parent_id);
CREATE INDEX idx_messages_channel_id ON messages(channel_id);
CREATE INDEX idx_messages_author_id ON messages(author_id);
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_attachments_message_id ON attachments(message_id);
CREATE INDEX idx_reactions_message_id ON reactions(message_id);
CREATE INDEX idx_typing_indicators_channel_id ON typing_indicators(channel_id);
CREATE INDEX idx_channel_permissions_channel_id ON channel_permissions(channel_id);

-- Functions and triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_servers_updated_at BEFORE UPDATE ON servers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, username, display_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1))
    );
    
    INSERT INTO user_presence (user_id, status)
    VALUES (NEW.id, 'online');
    
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to clean up old typing indicators
CREATE OR REPLACE FUNCTION cleanup_typing_indicators()
RETURNS void AS $$
BEGIN
    DELETE FROM typing_indicators 
    WHERE started_at < NOW() - INTERVAL '10 seconds';
END;
$$ language 'plpgsql';

-- Function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(user_uuid UUID, new_status user_status)
RETURNS void AS $$
BEGIN
    INSERT INTO user_presence (user_id, status, last_seen, updated_at)
    VALUES (user_uuid, new_status, NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        status = new_status,
        last_seen = NOW(),
        updated_at = NOW();
        
    UPDATE profiles 
    SET status = new_status, last_seen = NOW()
    WHERE id = user_uuid;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE servers ENABLE ROW LEVEL SECURITY;
ALTER TABLE server_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE channel_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE server_invites ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view all profiles" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for servers
CREATE POLICY "Users can view servers they are members of" ON servers
    FOR SELECT USING (
        id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Server owners can update their servers" ON servers
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Authenticated users can create servers" ON servers
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- RLS Policies for server_members
CREATE POLICY "Users can view members of servers they belong to" ON server_members
    FOR SELECT USING (
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Server owners and admins can manage members" ON server_members
    FOR ALL USING (
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
            AND role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Users can join servers via invite" ON server_members
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for channels
CREATE POLICY "Users can view channels in their servers" ON channels
    FOR SELECT USING (
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
        )
        AND (
            NOT is_private OR
            id IN (
                SELECT channel_id FROM channel_permissions
                WHERE user_id = auth.uid() AND can_read = true
            )
        )
    );

CREATE POLICY "Server owners and admins can create channels" ON channels
    FOR INSERT WITH CHECK (
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
            AND role IN ('owner', 'admin', 'moderator')
        )
    );

CREATE POLICY "Server owners and admins can manage channels" ON channels
    FOR ALL USING (
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
            AND role IN ('owner', 'admin', 'moderator')
        )
    );

-- RLS Policies for messages
CREATE POLICY "Users can view messages in accessible channels" ON messages
    FOR SELECT USING (
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
            AND (
                NOT c.is_private OR
                c.id IN (
                    SELECT channel_id FROM channel_permissions
                    WHERE user_id = auth.uid() AND can_read = true
                )
            )
        )
    );

CREATE POLICY "Users can create messages in writable channels" ON messages
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
            AND (
                NOT c.is_private OR
                c.id IN (
                    SELECT channel_id FROM channel_permissions
                    WHERE user_id = auth.uid() AND can_write = true
                )
            )
        )
    );

CREATE POLICY "Users can update their own messages" ON messages
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Users can delete their own messages or moderators can delete any" ON messages
    FOR DELETE USING (
        auth.uid() = author_id OR
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
            AND sm.role IN ('owner', 'admin', 'moderator')
        )
    );

-- RLS Policies for attachments
CREATE POLICY "Users can view attachments for accessible messages" ON attachments
    FOR SELECT USING (
        message_id IN (
            SELECT id FROM messages
        )
    );

CREATE POLICY "Users can create attachments for their messages" ON attachments
    FOR INSERT WITH CHECK (
        message_id IN (
            SELECT id FROM messages WHERE author_id = auth.uid()
        )
    );

-- RLS Policies for reactions
CREATE POLICY "Users can view reactions on accessible messages" ON reactions
    FOR SELECT USING (
        message_id IN (
            SELECT id FROM messages
        )
    );

CREATE POLICY "Users can manage their own reactions" ON reactions
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for user_presence
CREATE POLICY "Users can view all user presence" ON user_presence
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own presence" ON user_presence
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own presence" ON user_presence
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for typing_indicators
CREATE POLICY "Users can view typing indicators in accessible channels" ON typing_indicators
    FOR SELECT USING (
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own typing indicators" ON typing_indicators
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for channel_permissions
CREATE POLICY "Users can view permissions for accessible channels" ON channel_permissions
    FOR SELECT USING (
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Server admins can create channel permissions" ON channel_permissions
    FOR INSERT WITH CHECK (
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
            AND sm.role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Server admins can manage channel permissions" ON channel_permissions
    FOR ALL USING (
        channel_id IN (
            SELECT c.id FROM channels c
            JOIN server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
            AND sm.role IN ('owner', 'admin')
        )
    );

-- RLS Policies for server_invites
CREATE POLICY "Users can view invites for their servers" ON server_invites
    FOR SELECT USING (
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Server members can create invites" ON server_invites
    FOR INSERT WITH CHECK (
        auth.uid() = inviter_id AND
        server_id IN (
            SELECT server_id FROM server_members
            WHERE user_id = auth.uid()
        )
    );

-- Create realtime publications
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
ALTER PUBLICATION supabase_realtime ADD TABLE user_presence;
ALTER PUBLICATION supabase_realtime ADD TABLE typing_indicators;
ALTER PUBLICATION supabase_realtime ADD TABLE reactions;
ALTER PUBLICATION supabase_realtime ADD TABLE server_members;
